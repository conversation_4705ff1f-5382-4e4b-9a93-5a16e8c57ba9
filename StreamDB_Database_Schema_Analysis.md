# StreamDB Database Schema Analysis & Implementation Guide

## 📊 Current Database Overview

**Database**: `stream_db`  
**Server**: *********** (Backend Production)  
**Connection**: MySQL via socket `/var/run/mysqld/mysqld.sock`  
**Total Tables**: 17

---

## 🗂️ Complete Table Structure

### Core Content Tables

#### 1. `content` (Primary Content Table)
```sql
CREATE TABLE content (
    id VARCHAR(50) PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    year INT NOT NULL,
    type ENUM('movie','series','requested') NOT NULL,
    category VARCHAR(100),
    section_id INT,  -- Legacy single section reference
    image VARCHAR(500),
    cover_image VARCHAR(500),
    tmdb_id VARCHAR(20),
    poster_url VARCHAR(500),
    thumbnail_url VARCHAR(500),
    secure_video_links TEXT,
    imdb_rating DECIMAL(3,1),
    runtime VARCHAR(20),
    studio VARCHAR(255),
    tags TEXT,
    trailer VARCHAR(500),
    subtitle_url VARCHAR(500),
    is_published TINYINT(1) DEFAULT 0,
    is_featured TINYINT(1) DEFAULT 0,
    add_to_carousel TINYINT(1) DEFAULT 0,
    total_seasons INT DEFAULT 0,
    total_episodes INT DEFAULT 0,
    languages JSON,
    genres JSON,
    quality JSON,
    audio_tracks JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_title (title),
    INDEX idx_year (year),
    INDEX idx_type (type),
    INDEX idx_category (category),
    INDEX idx_section_id (section_id),
    INDEX idx_tmdb_id (tmdb_id),
    INDEX idx_published (is_published),
    INDEX idx_featured (is_featured),
    INDEX idx_carousel (add_to_carousel),
    INDEX idx_created (created_at)
);
```

#### 2. `content_sections` (Section Definitions)
```sql
CREATE TABLE content_sections (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    slug VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    icon VARCHAR(50),
    color VARCHAR(20),
    display_order INT DEFAULT 0,
    is_active TINYINT(1) DEFAULT 1,
    show_in_navigation TINYINT(1) DEFAULT 1,
    show_on_homepage TINYINT(1) DEFAULT 1,
    max_items_homepage INT DEFAULT 20,
    content_types JSON,  -- ["movie", "series", "requested"]
    filter_rules JSON,   -- {"type": "movie", "category": "action"}
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_display_order (display_order),
    INDEX idx_active (is_active),
    INDEX idx_homepage (show_on_homepage)
);
```

#### 3. `content_section_mappings` (Many-to-Many Relationships)
```sql
CREATE TABLE content_section_mappings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    content_id VARCHAR(50) NOT NULL,
    section_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE,
    FOREIGN KEY (section_id) REFERENCES content_sections(id) ON DELETE CASCADE,
    UNIQUE KEY unique_content_section (content_id, section_id),
    INDEX idx_content_id (content_id),
    INDEX idx_section_id (section_id)
);
```

### Web Series Management Tables

#### 4. `seasons` (Web Series Seasons)
```sql
CREATE TABLE seasons (
    id INT PRIMARY KEY AUTO_INCREMENT,
    content_id VARCHAR(50) NOT NULL,
    season_number INT NOT NULL,
    title VARCHAR(255),  -- Optional
    description TEXT,    -- Optional
    poster_url VARCHAR(500),  -- Optional
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE,
    UNIQUE KEY unique_content_season (content_id, season_number),
    INDEX idx_content_id (content_id),
    INDEX idx_season_number (season_number)
);
```

#### 5. `episodes` (Web Series Episodes)
```sql
CREATE TABLE episodes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    season_id INT NOT NULL,
    content_id VARCHAR(50) NOT NULL,  -- Denormalized for performance
    episode_number INT NOT NULL,
    title VARCHAR(255) NOT NULL,  -- Required
    description TEXT,             -- Optional
    video_embed_link TEXT NOT NULL,  -- Required
    duration VARCHAR(20),         -- Optional
    air_date DATE,               -- Optional
    thumbnail_url VARCHAR(500),  -- Optional
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (season_id) REFERENCES seasons(id) ON DELETE CASCADE,
    FOREIGN KEY (content_id) REFERENCES content(id) ON DELETE CASCADE,
    UNIQUE KEY unique_season_episode (season_id, episode_number),
    INDEX idx_season_id (season_id),
    INDEX idx_content_id (content_id),
    INDEX idx_episode_number (episode_number)
);
```

### Category & Classification Tables

#### 6. `categories` (Content Categories)
```sql
CREATE TABLE categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    slug VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    icon VARCHAR(50),
    color VARCHAR(20),
    is_active TINYINT(1) DEFAULT 1,
    display_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_active (is_active),
    INDEX idx_display_order (display_order)
);
```

#### 7. `section_categories` (Section-Category Relationships)
```sql
CREATE TABLE section_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    section_id INT NOT NULL,
    category_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (section_id) REFERENCES content_sections(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
    UNIQUE KEY unique_section_category (section_id, category_id)
);
```

#### 8. `section_content_types` (Section Content Type Mappings)
```sql
CREATE TABLE section_content_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    section_id INT NOT NULL,
    content_type ENUM('movie','series','requested') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (section_id) REFERENCES content_sections(id) ON DELETE CASCADE,
    UNIQUE KEY unique_section_type (section_id, content_type)
);
```

---

## 📋 Current Data Analysis

### Existing Content Sections
```sql
-- Current sections in production
INSERT INTO content_sections VALUES
(1, 'Movies', 'movies', 'Latest and popular movies collection', 'Film', '#e11d48', 2, 1, 1, 1, 10, '["movie"]', '{"type": "movie"}'),
(2, 'Web Series', 'web-series', 'Trending web series and TV shows', 'Tv', '#3b82f6', 3, 1, 1, 1, 10, '["series"]', '{"type": "series"}'),
(3, 'Requested', 'requested', 'User requested content', 'Clock', '#f59e0b', 4, 1, 1, 1, 10, '["requested"]', '{"type": "requested"}'),
(4, 'New Releases', 'new-releases', '', 'Folder', '#10b981', 1, 1, 1, 1, 10, '["movie", "series"]', '{}'),
(6, 'Drama', 'drama', '', 'Film', '#8b5cf6', 4, 1, 1, 1, 10, '[]', '{}');
```

### Current Content Data
```sql
-- Sample content currently in database
content_1752119328013_7805dkzrp | Countdown | Web Series (section_id: 2)
content_1752122972013_2beif29uf | Ballerina | Movies (section_id: 1)  
content_1752129612931_45aclkm49 | F1        | New Releases (section_id: 4)
```

### Current Section Mappings
```sql
-- Existing mappings in content_section_mappings
(1, 'content_1752122972013_2beif29uf', 1)  -- Ballerina -> Movies
(2, 'content_1752119328013_7805dkzrp', 2)  -- Countdown -> Web Series
```

---

## 🔧 Implementation Strategy

### Phase 1: Multiple Section Support (Current Priority)

#### A. Database Consistency Check
```sql
-- Ensure all content has mappings
INSERT INTO content_section_mappings (content_id, section_id)
SELECT c.id, c.section_id 
FROM content c 
WHERE c.section_id IS NOT NULL 
AND NOT EXISTS (
    SELECT 1 FROM content_section_mappings csm 
    WHERE csm.content_id = c.id AND csm.section_id = c.section_id
);
```

#### B. API Endpoint Updates Required
1. **Content Creation**: Support multiple section IDs in request
2. **Content Update**: Handle section mapping changes
3. **Content Retrieval**: Join with mappings table
4. **Section Management**: CRUD operations for sections

#### C. Frontend Form Updates
1. **Add New Content**: Multi-select dropdown for sections
2. **Manage Content**: Edit existing section assignments
3. **Admin Panel**: Section management interface

### Phase 2: Data Migration & Cleanup

#### A. Migrate Legacy Section References
```sql
-- Create procedure to sync legacy section_id with mappings
DELIMITER //
CREATE PROCEDURE SyncLegacySections()
BEGIN
    -- Insert missing mappings from legacy section_id
    INSERT IGNORE INTO content_section_mappings (content_id, section_id)
    SELECT id, section_id FROM content WHERE section_id IS NOT NULL;
    
    -- Update content total counts
    UPDATE content_sections cs SET 
        cs.max_items_homepage = (
            SELECT COUNT(*) FROM content_section_mappings csm 
            WHERE csm.section_id = cs.id
        );
END //
DELIMITER ;
```

#### B. Data Validation Queries
```sql
-- Check for orphaned mappings
SELECT csm.* FROM content_section_mappings csm
LEFT JOIN content c ON csm.content_id = c.id
WHERE c.id IS NULL;

-- Check for missing primary sections
SELECT c.* FROM content c
LEFT JOIN content_section_mappings csm ON c.id = csm.content_id
WHERE csm.content_id IS NULL AND c.is_published = 1;
```

---

## ✅ Future-Proof Implementation

### 1. Content Addition Workflow
```sql
-- When adding new content:
BEGIN TRANSACTION;

-- 1. Insert content
INSERT INTO content (...) VALUES (...);

-- 2. Add section mappings
INSERT INTO content_section_mappings (content_id, section_id) VALUES
('new_content_id', 1),  -- Movies
('new_content_id', 4);  -- New Releases

-- 3. Update section counts (optional, can be calculated)
UPDATE content_sections SET updated_at = NOW() WHERE id IN (1, 4);

COMMIT;
```

### 2. Homepage Content Retrieval
```sql
-- Optimized query for homepage sections
SELECT 
    cs.id as section_id,
    cs.name as section_name,
    cs.slug,
    cs.color,
    cs.icon,
    c.*
FROM content_sections cs
JOIN content_section_mappings csm ON cs.id = csm.section_id
JOIN content c ON csm.content_id = c.id
WHERE cs.show_on_homepage = 1 
  AND cs.is_active = 1 
  AND c.is_published = 1
ORDER BY cs.display_order ASC, c.created_at DESC
LIMIT cs.max_items_homepage;
```

### 3. Performance Optimization
```sql
-- Recommended indexes for optimal performance
CREATE INDEX idx_content_published_created ON content(is_published, created_at DESC);
CREATE INDEX idx_section_mappings_lookup ON content_section_mappings(section_id, content_id);
CREATE INDEX idx_sections_homepage ON content_sections(show_on_homepage, is_active, display_order);
```

---

## 🚀 Benefits of Current Schema

### ✅ Advantages
1. **Flexible Section Assignment**: Content can appear in multiple sections
2. **Backward Compatibility**: Legacy `section_id` preserved
3. **Performance Optimized**: Proper indexing for fast queries
4. **Scalable**: Easy to add new sections and relationships
5. **Data Integrity**: Foreign key constraints prevent orphaned data

### ⚠️ Considerations
1. **Dual System**: Both `section_id` and mappings table need synchronization
2. **Migration Required**: Existing content needs mapping entries
3. **API Updates**: Backend endpoints need modification for multiple sections
4. **Frontend Changes**: UI components need multi-select capability

---

## 📊 Recommended Next Steps

1. **Immediate**: Complete data migration to populate `content_section_mappings`
2. **Backend**: Update API endpoints to handle multiple sections
3. **Frontend**: Implement multi-select section dropdowns
4. **Testing**: Verify homepage content display with multiple sections
5. **Optimization**: Monitor query performance and add indexes as needed

The current database schema is well-designed and ready to support multiple content sections with minimal modifications required.

---

## 🔐 Authentication & Security Tables

### 9. `admin_users` (Admin Authentication)
```sql
CREATE TABLE admin_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    is_active TINYINT(1) DEFAULT 1,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_username (username),
    INDEX idx_active (is_active)
);
```

### 10. `sessions` (User Session Management)
```sql
CREATE TABLE sessions (
    id VARCHAR(128) PRIMARY KEY,
    user_id INT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    payload TEXT,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_last_activity (last_activity),
    INDEX idx_expires (expires_at)
);
```

### 11. `auth_tokens` (API Authentication)
```sql
CREATE TABLE auth_tokens (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    token_hash VARCHAR(255) NOT NULL,
    token_type ENUM('access', 'refresh', 'api') DEFAULT 'access',
    expires_at TIMESTAMP,
    is_revoked TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE CASCADE,
    INDEX idx_token_hash (token_hash),
    INDEX idx_user_id (user_id),
    INDEX idx_expires (expires_at)
);
```

### 12. `security_logs` (Security Event Logging)
```sql
CREATE TABLE security_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    event_type VARCHAR(50) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    details JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE SET NULL,
    INDEX idx_event_type (event_type),
    INDEX idx_user_id (user_id),
    INDEX idx_created (created_at)
);
```

---

## 🛡️ Additional System Tables

### 13. `login_attempts` (Brute Force Protection)
```sql
CREATE TABLE login_attempts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    ip_address VARCHAR(45) NOT NULL,
    username VARCHAR(50),
    success TINYINT(1) DEFAULT 0,
    attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_ip_address (ip_address),
    INDEX idx_username (username),
    INDEX idx_attempted (attempted_at)
);
```

### 14. `password_reset_tokens` (Password Recovery)
```sql
CREATE TABLE password_reset_tokens (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    token VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    used_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE CASCADE,
    INDEX idx_token (token),
    INDEX idx_user_id (user_id),
    INDEX idx_expires (expires_at)
);
```

### 15. `user_sessions` (Extended Session Data)
```sql
CREATE TABLE user_sessions (
    id VARCHAR(128) PRIMARY KEY,
    user_id INT,
    session_data TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_expires (expires_at)
);
```

### 16. `admin_security_logs` (Admin-Specific Security)
```sql
CREATE TABLE admin_security_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    admin_id INT,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50),
    resource_id VARCHAR(50),
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (admin_id) REFERENCES admin_users(id) ON DELETE SET NULL,
    INDEX idx_admin_id (admin_id),
    INDEX idx_action (action),
    INDEX idx_resource (resource_type, resource_id),
    INDEX idx_created (created_at)
);
```

### 17. `ad_blocker_tracking` (Analytics/Tracking)
```sql
CREATE TABLE ad_blocker_tracking (
    id INT PRIMARY KEY AUTO_INCREMENT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    has_adblocker TINYINT(1) DEFAULT 0,
    page_url VARCHAR(500),
    referrer VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_ip_address (ip_address),
    INDEX idx_has_adblocker (has_adblocker),
    INDEX idx_created (created_at)
);
```

---

## 🔄 Data Relationships & Constraints

### Primary Relationships
```sql
-- Content to Sections (Many-to-Many)
content ←→ content_section_mappings ←→ content_sections

-- Content to Episodes (One-to-Many via Seasons)
content → seasons → episodes

-- Sections to Categories (Many-to-Many)
content_sections ←→ section_categories ←→ categories

-- Admin Authentication Chain
admin_users → sessions
admin_users → auth_tokens
admin_users → security_logs
```

### Referential Integrity Rules
```sql
-- Cascade Deletes
ON DELETE CASCADE: content_section_mappings, seasons, episodes
ON DELETE SET NULL: security_logs, admin_security_logs

-- Unique Constraints
UNIQUE: content_section_mappings(content_id, section_id)
UNIQUE: seasons(content_id, season_number)
UNIQUE: episodes(season_id, episode_number)
```

---

## 📈 Performance Optimization Strategy

### Critical Indexes for Production
```sql
-- Homepage Performance
CREATE INDEX idx_homepage_content ON content_section_mappings(section_id)
    INCLUDE (content_id);

-- Search Performance
CREATE FULLTEXT INDEX idx_content_search ON content(title, description);

-- Admin Panel Performance
CREATE INDEX idx_admin_content_management ON content(type, is_published, created_at DESC);

-- Analytics Performance
CREATE INDEX idx_content_stats ON content(created_at, type, is_published);
```

### Query Optimization Examples
```sql
-- Optimized Homepage Query (with caching consideration)
SELECT
    cs.id, cs.name, cs.slug, cs.color, cs.icon,
    c.id, c.title, c.poster_url, c.year, c.imdb_rating
FROM content_sections cs
INNER JOIN content_section_mappings csm ON cs.id = csm.section_id
INNER JOIN content c ON csm.content_id = c.id
WHERE cs.show_on_homepage = 1
  AND cs.is_active = 1
  AND c.is_published = 1
ORDER BY cs.display_order, c.created_at DESC;

-- Content Search with Multiple Sections
SELECT DISTINCT c.*, GROUP_CONCAT(cs.name) as sections
FROM content c
LEFT JOIN content_section_mappings csm ON c.id = csm.content_id
LEFT JOIN content_sections cs ON csm.section_id = cs.id
WHERE MATCH(c.title, c.description) AGAINST(? IN NATURAL LANGUAGE MODE)
  AND c.is_published = 1
GROUP BY c.id
ORDER BY c.created_at DESC;
```

---

## 🎯 Implementation Checklist

### ✅ Database Level (Complete)
- [x] All tables exist with proper structure
- [x] Foreign key constraints in place
- [x] Indexes optimized for performance
- [x] Data types appropriate for content

### 🔄 Backend API Level (Needs Updates)
- [ ] Update content creation endpoint for multiple sections
- [ ] Modify content update endpoint for section management
- [ ] Enhance content retrieval with section mappings
- [ ] Add section management CRUD endpoints
- [ ] Implement data migration scripts

### 🎨 Frontend Level (Needs Updates)
- [ ] Multi-select dropdown for content sections
- [ ] Section management interface in admin panel
- [ ] Homepage rendering with multiple sections
- [ ] Content editing with section assignments

### 🧪 Testing & Validation
- [ ] Data migration testing
- [ ] Performance testing with large datasets
- [ ] Frontend integration testing
- [ ] API endpoint validation

---

## 🚀 Production Deployment Strategy

### Phase 1: Data Migration (Zero Downtime)
1. Run migration script to populate `content_section_mappings`
2. Validate data consistency
3. Create backup of current state

### Phase 2: Backend Updates (Rolling Deployment)
1. Deploy updated API endpoints
2. Test with existing frontend
3. Monitor for errors

### Phase 3: Frontend Updates (Feature Flag)
1. Deploy new admin interface
2. Enable multiple section selection
3. Test content creation/editing

### Phase 4: Cleanup & Optimization
1. Remove legacy code paths
2. Optimize database queries
3. Monitor performance metrics

The database schema is production-ready and will scale effectively for future content growth and feature additions.
