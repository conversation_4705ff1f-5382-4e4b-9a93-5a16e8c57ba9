const express = require('express');
const router = express.Router();
const db = require('../config/database');
const { body, validationResult } = require('express-validator');

// Helper function to generate unique IDs
function generateId(prefix) {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 11);
  return `${prefix}_${timestamp}_${random}`;
}

// Helper function to get next available season number
async function getNextSeasonNumber(contentId) {
  try {
    const result = await db.execute(
      'SELECT MAX(season_number) as max_season FROM seasons WHERE content_id = ?',
      [contentId]
    );
    // Handle mysql2 result format - result is already the rows array
    const rows = Array.isArray(result) ? result : [];
    return (rows[0]?.max_season || 0) + 1;
  } catch (error) {
    console.error('Error getting next season number:', error);
    return 1;
  }
}

// Helper function to get next available episode number
async function getNextEpisodeNumber(seasonId) {
  try {
    const result = await db.execute(
      'SELECT MAX(episode_number) as max_episode FROM episodes WHERE season_id = ?',
      [seasonId]
    );
    // Handle mysql2 result format - result is already the rows array
    const rows = Array.isArray(result) ? result : [];
    return (rows[0]?.max_episode || 0) + 1;
  } catch (error) {
    console.error('Error getting next episode number:', error);
    return 1;
  }
}

// ==================== GENERAL ENDPOINTS ====================

// GET all episodes (general endpoint)
router.get('/episodes', async (req, res) => {
  try {
    const episodes = await db.execute(
      `SELECT e.*, s.season_number, s.title as season_title, s.web_series_title, c.title as content_title
       FROM episodes e
       JOIN seasons s ON e.season_id = s.id
       JOIN content c ON e.content_id = c.id
       ORDER BY c.title ASC, s.season_number ASC, e.episode_number ASC`
    );

    // Ensure we always return an array
    const episodesData = Array.isArray(episodes) ? episodes : [];

    res.json({
      success: true,
      data: episodesData,
      total: episodesData.length
    });
  } catch (error) {
    console.error('Error fetching all episodes:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch episodes',
      error: error.message,
      data: [] // Always provide empty array on error
    });
  }
});

// GET all seasons (general endpoint)
router.get('/seasons', async (req, res) => {
  try {
    const seasons = await db.execute(
      `SELECT s.*, c.title as content_title, COUNT(e.id) as episode_count
       FROM seasons s
       JOIN content c ON s.content_id = c.id
       LEFT JOIN episodes e ON s.id = e.season_id
       GROUP BY s.id
       ORDER BY c.title ASC, s.season_number ASC`
    );

    // Ensure we always return an array
    const seasonsData = Array.isArray(seasons) ? seasons : [];

    res.json({
      success: true,
      data: seasonsData,
      total: seasonsData.length
    });
  } catch (error) {
    console.error('Error fetching all seasons:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch seasons',
      error: error.message,
      data: [] // Always provide empty array on error
    });
  }
});

// ==================== SEASONS CRUD OPERATIONS ====================

// GET all seasons for a content WITH episodes
router.get('/content/:contentId/seasons', async (req, res) => {
  try {
    const { contentId } = req.params;

    // First get all seasons
    const seasons = await db.execute(
      `SELECT s.*, COUNT(e.id) as episode_count
       FROM seasons s
       LEFT JOIN episodes e ON s.id = e.season_id
       WHERE s.content_id = ?
       GROUP BY s.id
       ORDER BY s.season_number ASC`,
      [contentId]
    );

    // Then get episodes for each season
    const seasonsWithEpisodes = [];
    for (const season of seasons) {
      const episodes = await db.execute(
        'SELECT * FROM episodes WHERE season_id = ? ORDER BY episode_number ASC',
        [season.id]
      );
      
      seasonsWithEpisodes.push({
        ...season,
        episodes: episodes || []
      });
    }

    res.json({
      success: true,
      data: seasonsWithEpisodes
    });
  } catch (error) {
    console.error('Error fetching seasons:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch seasons',
      error: error.message,
      data: [] // Always provide empty array on error
    });
  }
});

// GET single season
router.get('/content/:contentId/seasons/:seasonId', async (req, res) => {
  try {
    const { seasonId } = req.params;

    const season = await db.execute(
      'SELECT * FROM seasons WHERE id = ?',
      [seasonId]
    );

    if (!season.length) {
      return res.status(404).json({
        success: false,
        message: 'Season not found'
      });
    }

    res.json({
      success: true,
      data: season[0]
    });
  } catch (error) {
    console.error('Error fetching season:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch season',
      error: error.message
    });
  }
});

// POST create new season
router.post('/content/:contentId/seasons', [
  body('seasonNumber').optional().isInt({ min: 1 }),
  body('title').optional().isString().trim(),
  body('description').optional().isString().trim()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { contentId } = req.params;
    const { seasonNumber, title, description } = req.body;

    // Check if content exists and get its title
    const contentCheck = await db.execute(
      'SELECT id, title FROM content WHERE id = ?',
      [contentId]
    );

    if (!contentCheck.length) {
      return res.status(404).json({
        success: false,
        message: 'Content not found'
      });
    }

    const webSeriesTitle = contentCheck[0].title;

    // Determine season number
    let finalSeasonNumber = seasonNumber;
    if (!finalSeasonNumber) {
      finalSeasonNumber = await getNextSeasonNumber(contentId);
    } else {
      // Check if season number already exists
      const existingCheck = await db.execute(
        'SELECT id FROM seasons WHERE content_id = ? AND season_number = ?',
        [contentId, finalSeasonNumber]
      );

      if (existingCheck.length) {
        console.log(`Season ${finalSeasonNumber} exists, using next available: ${await getNextSeasonNumber(contentId)}`);
        finalSeasonNumber = await getNextSeasonNumber(contentId);
      }
    }

    const seasonId = generateId('season');

    await db.execute(
      `INSERT INTO seasons (id, content_id, web_series_title, season_number, title, description, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [seasonId, contentId, webSeriesTitle, finalSeasonNumber, title || null, description || null]
    );

    const newSeason = await db.execute(
      'SELECT * FROM seasons WHERE id = ?',
      [seasonId]
    );

    res.status(201).json({
      success: true,
      message: 'Season created successfully',
      data: newSeason[0]
    });
  } catch (error) {
    console.error('Error creating season:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create season',
      error: error.message
    });
  }
});

// PUT update season
router.put('/content/:contentId/seasons/:seasonId', [
  body('seasonNumber').optional().isInt({ min: 1 }),
  body('title').optional().isString().trim(),
  body('description').optional().isString().trim()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { seasonId } = req.params;
    const { seasonNumber, title, description } = req.body;

    // Check if season exists
    const seasonCheck = await db.execute(
      'SELECT * FROM seasons WHERE id = ?',
      [seasonId]
    );

    if (!seasonCheck.length) {
      return res.status(404).json({
        success: false,
        message: 'Season not found'
      });
    }

    // Build update query dynamically
    const updates = [];
    const values = [];

    if (seasonNumber !== undefined) {
      updates.push('season_number = ?');
      values.push(seasonNumber);
    }
    if (title !== undefined) {
      updates.push('title = ?');
      values.push(title);
    }
    if (description !== undefined) {
      updates.push('description = ?');
      values.push(description);
    }

    if (updates.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No fields to update'
      });
    }

    updates.push('updated_at = NOW()');
    values.push(seasonId);

    await db.execute(
      `UPDATE seasons SET ${updates.join(', ')} WHERE id = ?`,
      values
    );

    const updatedSeason = await db.execute(
      'SELECT * FROM seasons WHERE id = ?',
      [seasonId]
    );

    res.json({
      success: true,
      message: 'Season updated successfully',
      data: updatedSeason[0]
    });
  } catch (error) {
    console.error('Error updating season:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update season',
      error: error.message
    });
  }
});

// DELETE season
router.delete('/content/:contentId/seasons/:seasonId', async (req, res) => {
  try {
    const { seasonId } = req.params;

    // Check if season exists
    const seasonCheck = await db.execute(
      'SELECT * FROM seasons WHERE id = ?',
      [seasonId]
    );

    if (!seasonCheck.length) {
      return res.status(404).json({
        success: false,
        message: 'Season not found'
      });
    }

    // Delete all episodes in this season first
    await db.execute(
      'DELETE FROM episodes WHERE season_id = ?',
      [seasonId]
    );

    // Delete the season
    await db.execute(
      'DELETE FROM seasons WHERE id = ?',
      [seasonId]
    );

    res.json({
      success: true,
      message: 'Season and all its episodes deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting season:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete season',
      error: error.message
    });
  }
});

// ==================== EPISODES CRUD OPERATIONS ====================

// GET all episodes for a season
router.get('/content/:contentId/seasons/:seasonId/episodes', async (req, res) => {
  try {
    const { seasonId } = req.params;

    const episodes = await db.execute(
      'SELECT * FROM episodes WHERE season_id = ? ORDER BY episode_number ASC',
      [seasonId]
    );

    // Ensure we always return an array
    const episodesData = Array.isArray(episodes) ? episodes : [];

    res.json({
      success: true,
      data: episodesData
    });
  } catch (error) {
    console.error('Error fetching episodes:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch episodes',
      error: error.message,
      data: [] // Always provide empty array on error
    });
  }
});

// GET single episode
router.get('/content/:contentId/seasons/:seasonId/episodes/:episodeId', async (req, res) => {
  try {
    const { episodeId } = req.params;

    const episode = await db.execute(
      'SELECT * FROM episodes WHERE id = ?',
      [episodeId]
    );

    if (!episode.length) {
      return res.status(404).json({
        success: false,
        message: 'Episode not found'
      });
    }

    res.json({
      success: true,
      data: episode[0]
    });
  } catch (error) {
    console.error('Error fetching episode:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch episode',
      error: error.message
    });
  }
});

// POST create new episode
router.post('/content/:contentId/seasons/:seasonId/episodes', [
  body('title').notEmpty().withMessage('Title is required'),
  body('secureVideoLinks').optional().isString().trim(),
  body('episodeNumber').optional().isInt({ min: 1 }),
  body('description').optional().isString().trim(),
  body('runtime').optional().isString().trim(),
  body('airDate').optional().isISO8601()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { contentId, seasonId } = req.params;
    const {
      title,
      secureVideoLinks,
      episodeNumber,
      description,
      runtime,
      airDate
    } = req.body;

    // Check if season exists
    const seasonCheck = await db.execute(
      'SELECT * FROM seasons WHERE id = ? AND content_id = ?',
      [seasonId, contentId]
    );

    if (!seasonCheck.length) {
      return res.status(404).json({
        success: false,
        message: 'Season not found'
      });
    }

    // Determine episode number
    let finalEpisodeNumber = episodeNumber;
    if (!finalEpisodeNumber) {
      finalEpisodeNumber = await getNextEpisodeNumber(seasonId);
    } else {
      // Check if episode number already exists
      const existingCheck = await db.execute(
        'SELECT id FROM episodes WHERE season_id = ? AND episode_number = ?',
        [seasonId, finalEpisodeNumber]
      );

      if (existingCheck.length) {
        finalEpisodeNumber = await getNextEpisodeNumber(seasonId);
      }
    }

    const episodeId = generateId('episode');

    await db.execute(
      `INSERT INTO episodes (
        id, season_id, content_id, episode_number, title, description,
        secure_video_links, runtime, air_date, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [
        episodeId,
        seasonId,
        contentId,
        finalEpisodeNumber,
        title,
        description || null,
        secureVideoLinks,
        runtime || null,
        airDate || null
      ]
    );

    const newEpisode = await db.execute(
      'SELECT * FROM episodes WHERE id = ?',
      [episodeId]
    );

    res.status(201).json({
      success: true,
      message: 'Episode created successfully',
      data: newEpisode[0]
    });
  } catch (error) {
    console.error('Error creating episode:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create episode',
      error: error.message
    });
  }
});

// PUT update episode
router.put('/content/:contentId/seasons/:seasonId/episodes/:episodeId', [
  body('title').optional().isString().trim(),
  body('secureVideoLinks').optional().isString().trim(),
  body('episodeNumber').optional().isInt({ min: 1 }),
  body('description').optional().isString().trim(),
  body('runtime').optional().isString().trim(),
  body('airDate').optional().isISO8601()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { episodeId } = req.params;
    const {
      title,
      secureVideoLinks,
      episodeNumber,
      description,
      runtime,
      airDate
    } = req.body;

    // Check if episode exists
    const episodeCheck = await db.execute(
      'SELECT * FROM episodes WHERE id = ?',
      [episodeId]
    );

    if (!episodeCheck.length) {
      return res.status(404).json({
        success: false,
        message: 'Episode not found'
      });
    }

    // Build update query dynamically
    const updates = [];
    const values = [];

    if (title !== undefined) {
      updates.push('title = ?');
      values.push(title);
    }
    if (secureVideoLinks !== undefined) {
      updates.push('secure_video_links = ?');
      values.push(secureVideoLinks);
    }
    if (episodeNumber !== undefined) {
      updates.push('episode_number = ?');
      values.push(episodeNumber);
    }
    if (description !== undefined) {
      updates.push('description = ?');
      values.push(description);
    }
    if (runtime !== undefined) {
      updates.push('runtime = ?');
      values.push(runtime);
    }
    if (airDate !== undefined) {
      updates.push('air_date = ?');
      values.push(airDate);
    }

    if (updates.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No fields to update'
      });
    }

    updates.push('updated_at = NOW()');
    values.push(episodeId);

    await db.execute(
      `UPDATE episodes SET ${updates.join(', ')} WHERE id = ?`,
      values
    );

    const updatedEpisode = await db.execute(
      'SELECT * FROM episodes WHERE id = ?',
      [episodeId]
    );

    res.json({
      success: true,
      message: 'Episode updated successfully',
      data: updatedEpisode[0]
    });
  } catch (error) {
    console.error('Error updating episode:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update episode',
      error: error.message
    });
  }
});

// DELETE episode
router.delete('/content/:contentId/seasons/:seasonId/episodes/:episodeId', async (req, res) => {
  try {
    const { episodeId } = req.params;

    // Check if episode exists
    const episodeCheck = await db.execute(
      'SELECT * FROM episodes WHERE id = ?',
      [episodeId]
    );

    if (!episodeCheck.length) {
      return res.status(404).json({
        success: false,
        message: 'Episode not found'
      });
    }

    // Delete the episode
    await db.execute(
      'DELETE FROM episodes WHERE id = ?',
      [episodeId]
    );

    res.json({
      success: true,
      message: 'Episode deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting episode:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete episode',
      error: error.message
    });
  }
});

// ==================== BULK OPERATIONS ====================

// GET all episodes for a content (across all seasons)
router.get('/content/:contentId/episodes', async (req, res) => {
  try {
    const { contentId } = req.params;

    const episodes = await db.execute(
      `SELECT e.*, s.season_number, s.title as season_title, s.web_series_title
       FROM episodes e
       JOIN seasons s ON e.season_id = s.id
       WHERE e.content_id = ?
       ORDER BY s.season_number ASC, e.episode_number ASC`,
      [contentId]
    );

    // Ensure we always return an array
    const episodesData = Array.isArray(episodes) ? episodes : [];

    res.json({
      success: true,
      data: episodesData
    });
  } catch (error) {
    console.error('Error fetching all episodes:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch episodes',
      error: error.message,
      data: [] // Always provide empty array on error
    });
  }
});

module.exports = router;
